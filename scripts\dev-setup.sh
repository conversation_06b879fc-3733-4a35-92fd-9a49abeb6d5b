#!/bin/bash

# CanMoms Development Setup Script
echo "🚀 Setting up CanMoms development environment..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed."
    exit 1
fi

echo "✅ npm version: $(npm -v)"

# Install root dependencies
echo "📦 Installing root dependencies..."
npm install

# Install dependencies for all workspaces
echo "📦 Installing workspace dependencies..."
npm run install:all

# Build shared packages
echo "🔨 Building shared packages..."
npm run build --workspace=packages/types
npm run build --workspace=packages/utils

# Setup environment files
echo "⚙️  Setting up environment files..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ Created .env file from .env.example"
    echo "⚠️  Please update the .env file with your configuration"
else
    echo "✅ .env file already exists"
fi

# Setup git hooks
echo "🪝 Setting up git hooks..."
npx husky install
chmod +x .husky/pre-commit

# Create logs directory
mkdir -p logs
echo "✅ Created logs directory"

# Setup database (if Docker is available)
if command -v docker &> /dev/null; then
    echo "🐳 Docker detected. You can run 'docker-compose up -d postgres redis' to start the database services."
else
    echo "⚠️  Docker not found. Please install PostgreSQL and Redis manually or install Docker."
fi

echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "Next steps:"
echo "1. Update the .env file with your configuration"
echo "2. Start the database services: docker-compose up -d postgres redis"
echo "3. Start development servers: npm run dev"
echo ""
echo "Available commands:"
echo "  npm run dev          - Start web and backend in development mode"
echo "  npm run build        - Build all applications"
echo "  npm run test         - Run tests for all applications"
echo "  npm run lint         - Lint all applications"
echo ""
echo "Happy coding! 👩‍💻👨‍💻"

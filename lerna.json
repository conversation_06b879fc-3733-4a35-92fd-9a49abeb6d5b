{"$schema": "node_modules/lerna/schemas/lerna-schema.json", "version": "independent", "npmClient": "npm", "packages": ["apps/*"], "command": {"publish": {"conventionalCommits": true, "message": "chore(release): publish", "registry": "https://registry.npmjs.org/"}, "bootstrap": {"ignore": "component-*", "npmClientArgs": ["--no-package-lock"]}, "version": {"allowBranch": ["main", "master"], "conventionalCommits": true}}}
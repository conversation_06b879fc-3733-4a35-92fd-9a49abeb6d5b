# ---- Build Stage ----
# Use a Node.js image to build the app
FROM node:20-alpine AS build
WORKDIR /app

# Copy package files and install all dependencies (including dev)
COPY package*.json ./
RUN npm install

# Copy the rest of the source code
COPY . .

# Run the build script to compile TypeScript to JavaScript
RUN npm run build

# ---- Production Stage ----
# Use a smaller, production-ready Node.js image
FROM node:20-alpine AS production
WORKDIR /app

# Copy package files and install ONLY production dependencies
COPY package*.json ./
RUN npm install --production

# Copy the compiled code from the build stage
COPY --from=build /app/dist ./dist

# Expose the port the app runs on
EXPOSE 3000

# Command to run the application
CMD ["node", "dist/index.js"]
<?xml version="1.0" encoding="utf-8" ?>
<resources xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Application theme -->
    <style name="AppThemeBase21" parent="AppThemeBase">
        <!-- Uncomment this to make the app show underneat the status bar -->
        <!-- <item name="android:windowTranslucentStatus">true</item> -->

        <item name="android:datePickerStyle">@style/SpinnerDatePicker</item>
        <item name="android:timePickerStyle">@style/SpinnerTimePicker</item>
    </style>

    <style name="AppTheme" parent="AppThemeBase21">
    </style>

    <!-- Default style for DatePicker - in spinner mode -->
    <style name="SpinnerDatePicker" parent="android:Widget.Material.Light.DatePicker">
        <!-- set the default mode for the date picker (supported values: spinner, calendar)  -->
        <item name="android:datePickerMode">spinner</item>
    </style>

    <!-- Default style for TimePicker - in spinner mode -->
    <style name="SpinnerTimePicker" parent="android:Widget.Material.Light.TimePicker">
        <!-- set the default mode for the time picker (supported values: spinner, clock)  -->
        <item name="android:timePickerMode">spinner</item>
    </style>

    <style name="NativeScriptToolbarStyle" parent="NativeScriptToolbarStyleBase">
        <item name="android:elevation">4dp</item>

        <!-- Add padding to the ActionBar - useful when android:windowTranslucentStatus is set to true -->
        <!-- <item name="android:paddingTop">24dp</item> -->
    </style>
</resources>

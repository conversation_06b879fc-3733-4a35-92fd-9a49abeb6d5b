# Environment Configuration Example
# Copy this file to .env and update the values

# Application Environment
NODE_ENV=development
PORT=3000

# Database Configuration
DATABASE_URL=postgresql://canmoms:password@localhost:5432/canmoms_db
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=canmoms_db
DATABASE_USER=canmoms
DATABASE_PASSWORD=password

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your-super-secret-refresh-token-key
REFRESH_TOKEN_EXPIRES_IN=30d

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=CanMoms Platform

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# External APIs
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# AWS Configuration (if using AWS services)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=canmoms-uploads

# Frontend Configuration
FRONTEND_URL=http://localhost:4200
API_URL=http://localhost:3000

# Logging
LOG_LEVEL=debug
LOG_FILE=logs/app.log

# Security
CORS_ORIGIN=http://localhost:4200
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Development Tools
ENABLE_SWAGGER=true
ENABLE_PLAYGROUND=true

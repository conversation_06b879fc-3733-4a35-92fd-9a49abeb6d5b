# CanMoms Monorepo

A comprehensive platform for mothers built with modern technologies in a monorepo structure.

## 🏗️ Architecture

This monorepo contains multiple applications and shared packages:

### Applications (`apps/`)
- **`web/`** - Angular web application
- **`backend/`** - Node.js/TypeScript API server
- **`mobile/`** - NativeScript mobile application

### Shared Packages (`packages/`)
- **`shared/`** - Common utilities and components (to be created)
- **`types/`** - Shared TypeScript type definitions (to be created)
- **`utils/`** - Utility functions and helpers (to be created)

## 🚀 Getting Started

### Prerequisites
- Node.js >= 18.0.0
- npm >= 9.0.0

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd CanMoms
```

2. Install all dependencies:
```bash
npm run install:all
```

3. Bootstrap the monorepo:
```bash
npm run bootstrap
```

## 📜 Available Scripts

### Root Level Commands

- `npm run dev` - Start web and backend in development mode
- `npm run build` - Build all applications
- `npm run test` - Run tests for all applications
- `npm run lint` - Lint all applications
- `npm run clean` - Clean build artifacts

### Application Specific Commands

#### Web Application
- `npm run start:web` - Start the Angular web app
- `npm run build:web` - Build the web app for production
- `npm run test:web` - Run web app tests

#### Backend API
- `npm run start:backend` - Start the backend server
- `npm run build:backend` - Build the backend for production
- `npm run test:backend` - Run backend tests

#### Mobile Application
- `npm run start:mobile` - Start the mobile app
- `npm run build:mobile` - Build the mobile app
- `npm run test:mobile` - Run mobile app tests

## 🛠️ Development

### Code Style
This project uses ESLint and Prettier for code formatting and linting:

- `npm run lint` - Check for linting errors
- `npm run lint:fix` - Fix auto-fixable linting errors
- `npm run format` - Format code with Prettier

### Adding New Packages
To add a new shared package:

1. Create a new directory in `packages/`
2. Initialize with `npm init`
3. Add to workspace in root `package.json`
4. Update TypeScript paths in root `tsconfig.json`

## 📁 Project Structure

```
CanMoms/
├── apps/
│   ├── web/                 # Angular web application
│   ├── backend/             # Node.js API server
│   └── mobile/              # NativeScript mobile app
├── packages/                # Shared packages
│   ├── shared/              # Common utilities (to be created)
│   ├── types/               # TypeScript types (to be created)
│   └── utils/               # Utility functions (to be created)
├── package.json             # Root package.json with workspaces
├── lerna.json               # Lerna configuration
├── tsconfig.json            # Root TypeScript configuration
├── .eslintrc.js             # ESLint configuration
├── .prettierrc              # Prettier configuration
└── README.md                # This file
```

## 🔧 Technologies

- **Frontend**: Angular, TypeScript
- **Backend**: Node.js, TypeScript, Express
- **Mobile**: NativeScript, TypeScript
- **Monorepo**: npm workspaces, Lerna
- **Code Quality**: ESLint, Prettier, Husky
- **Testing**: Jest

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Team

CanMoms Development Team

---

For more detailed information about each application, check the README files in their respective directories.

{"compilerOptions": {"module": "esnext", "target": "ES2020", "moduleResolution": "node", "experimentalDecorators": true, "emitDecoratorMetadata": true, "noEmitHelpers": true, "noEmitOnError": true, "skipLibCheck": true, "lib": ["ESNext", "dom"], "baseUrl": ".", "paths": {"~/*": ["app/*"], "@/*": ["app/*"], "@canmoms/shared/*": ["../shared/src/*"]}}, "include": ["app/**/*"], "files": ["./references.d.ts"], "exclude": ["node_modules", "platforms"]}
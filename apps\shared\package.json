{"name": "@canmoms/shared", "version": "1.0.0", "description": "Shared TypeScript types and utility functions for CanMoms platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "files": ["dist"], "keywords": ["typescript", "types", "utilities", "canmoms"], "author": "CanMoms Team", "license": "MIT", "devDependencies": {"typescript": "^5.0.0", "rimraf": "^5.0.0", "@types/jest": "^29.0.0", "jest": "^29.0.0"}, "publishConfig": {"access": "restricted"}}
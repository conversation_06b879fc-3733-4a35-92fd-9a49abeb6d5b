{"name": "canmoms-backend", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "lint": "eslint 'src/**/*.ts'", "test": "jest", "test:watch": "jest --watch"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@canmoms/shared": "^1.0.0", "dotenv": "^17.2.0", "express": "^5.1.0"}, "devDependencies": {"@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.0.14", "eslint": "^9.31.0", "jest": "^30.0.4", "nodemon": "^3.1.10", "prettier": "^3.6.2", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}
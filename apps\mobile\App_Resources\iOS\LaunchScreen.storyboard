<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="13771" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina5_9" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13772"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="Llm-lL-Icb"/>
                        <viewControllerLayoutGuide type="bottom" id="xb3-aO-Qok"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="LaunchScreen.AspectFill" translatesAutoresizingMaskIntoConstraints="NO" id="wtH-rr-YfP">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                            </imageView>
                            <imageView userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="LaunchScreen.Center" translatesAutoresizingMaskIntoConstraints="NO" id="s1z-aa-wYv">
                                <rect key="frame" x="0.0" y="150" width="375" height="512"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="s1z-aa-wYv" secondAttribute="trailing" id="4Rf-kf-vql"/>
                            <constraint firstAttribute="bottom" secondItem="wtH-rr-YfP" secondAttribute="bottom" id="5CS-At-4ka"/>
                            <constraint firstItem="wtH-rr-YfP" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="Awn-b8-xf1"/>
                            <constraint firstItem="s1z-aa-wYv" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="BpJ-vb-eKA"/>
                            <constraint firstItem="s1z-aa-wYv" firstAttribute="trailing" secondItem="wtH-rr-YfP" secondAttribute="trailing" id="QIM-7J-OZz"/>
                            <constraint firstAttribute="trailing" secondItem="wtH-rr-YfP" secondAttribute="trailing" id="RXg-rW-UK8"/>
                            <constraint firstItem="s1z-aa-wYv" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" id="Rcp-LG-48z"/>
                            <constraint firstItem="wtH-rr-YfP" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="cch-8E-tYu"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="509.60000000000002" y="237.93103448275863"/>
        </scene>
    </scenes>
    <resources>
        <image name="LaunchScreen.AspectFill" width="768" height="1024"/>
        <image name="LaunchScreen.Center" width="384" height="512"/>
    </resources>
</document>

{"name": "canmoms-monorepo", "version": "1.0.0", "description": "CanMoms - A comprehensive platform for mothers", "private": true, "workspaces": ["apps/*"], "scripts": {"build": "npm run build --workspaces", "test": "npm run test --workspaces", "lint": "npm run lint --workspaces", "dev": "concurrently \"npm run dev --workspace=apps/web\" \"npm run dev --workspace=apps/backend\"", "start:web": "npm run start --workspace=apps/web", "start:backend": "npm run start --workspace=apps/backend", "start:mobile": "npm run start --workspace=apps/mobile", "build:web": "npm run build --workspace=apps/web", "build:backend": "npm run build --workspace=apps/backend", "build:mobile": "npm run build --workspace=apps/mobile", "test:web": "npm run test --workspace=apps/web", "test:backend": "npm run test --workspace=apps/backend", "test:mobile": "npm run test --workspace=apps/mobile", "clean": "npm run clean --workspaces --if-present", "install:all": "npm install", "bootstrap": "npm install && npm run build"}, "devDependencies": {"concurrently": "^8.2.2", "lerna": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "husky": "^8.0.0", "lint-staged": "^15.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/canmoms.git"}, "keywords": ["monorepo", "mothers", "community", "platform"], "author": "CanMoms Team", "license": "MIT"}